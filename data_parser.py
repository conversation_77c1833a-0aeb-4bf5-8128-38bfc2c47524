#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据解析模块
负责上交所和深交所L2数据的读取、解析和标准化
"""

import pandas as pd
import numpy as np
from datetime import datetime, time
from typing import Dict, List, Tuple, Optional, Iterator
import logging
from collections import defaultdict

logger = logging.getLogger(__name__)


class StandardizedOrder:
    """标准化订单结构"""

    def __init__(self, order_id: str, time: int, price: float, volume: int,
                 side: str, order_type: str, origin_no: str = None, date: str = None):
        self.order_id = order_id
        self.time = time
        self.price = price
        self.volume = volume
        self.side = side  # 'B' for buy, 'S' for sell
        self.order_type = order_type  # 'A' for add, 'D' for delete
        self.origin_no = origin_no  # 用于撤单时指定原订单号以及合并订单
        self.date = date  # 交易日期，用于数据隔离

    def to_dict(self) -> Dict:
        return {
            'order_id': self.order_id,
            'time': self.time,
            'price': self.price,
            'volume': self.volume,
            'side': self.side,
            'order_type': self.order_type,
            'origin_no': self.origin_no,
            'date': self.date
        }


class StandardizedTrade:
    """标准化成交结构"""

    def __init__(self, trade_id: str, time: int, price: float, volume: int,
                 buy_order_id: str, sell_order_id: str, bs_flag: Optional[str] = None, date: Optional[str] = None):
        self.trade_id = trade_id
        self.time = time
        self.price = price
        self.volume = volume
        self.buy_order_id = buy_order_id
        self.sell_order_id = sell_order_id
        self.bs_flag = bs_flag
        self.date = date  # 交易日期，用于数据隔离

    def to_dict(self) -> Dict:
        return {
            'trade_id': self.trade_id,
            'time': self.time,  
            'price': self.price,
            'volume': self.volume,
            'buy_order_id': self.buy_order_id,
            'sell_order_id': self.sell_order_id,
            'bs_flag': self.bs_flag,
            'date': self.date
        }


class TimeParser:
    """时间解析工具"""
    
    @staticmethod
    def parse_time_string(time_str: str) -> Tuple[int, int, int, int]:
        """解析时间字符串，返回(小时, 分钟, 秒, 毫秒)"""
        if not time_str:
            return 0, 0, 0, 0

        try:
            # 转换为字符串并去除小数点,并且填充为9位
            time_str = str(time_str).replace('.', '').zfill(9)


            # 标准格式：HHMMSSSS
            hour = int(time_str[:2])
            minute = int(time_str[2:4])
            second = int(time_str[4:6])
            millisecond = int(time_str[6:]) 
            


            return hour, minute, second, millisecond

        except (ValueError, IndexError) as e:
            # 解析失败时返回默认交易时间
            return 9, 30, 0, 0
    
    @staticmethod
    def get_trading_phase(time_str: str, market: str = 'shl2') -> str:
        """根据时间和市场确定交易阶段"""
        hour, minute, second, _ = TimeParser.parse_time_string(time_str)
        
        if hour == 0 and minute == 0:  # 解析失败的情况
            return 'UNKNOWN'
            
        time_obj = time(hour, minute, second)
        
        # 集合竞价时段
        if time(9, 15) <= time_obj <= time(9, 25):
            return 'C'  # 开盘集合竞价
        elif market == 'szl2' and time(14, 57) <= time_obj <= time(15, 0):
            return 'C'  # 深交所收盘集合竞价
        # 连续竞价时段
        elif time(9, 30) <= time_obj <= time(11, 30):
            return 'T'  # 上午连续竞价
        elif time(13, 0) <= time_obj <= time(14, 57):
            return 'T'  # 下午连续竞价
        elif market == 'shl2' and time(14, 57) <= time_obj <= time(15, 0):
            return 'T'  # 上交所默认连续竞价
        else:
            return 'S'  # 停牌或其他状态


class SHExchangeParser:
    """上交所数据解析器"""
    
    def __init__(self, security_id: str):
        self.security_id = security_id
        self.market = 'shl2'
        
    def parse_data_files(self, tick_file: str, trade_file: str, order_file: str) -> Tuple[List[StandardizedOrder], List[StandardizedTrade]]:
        """解析上交所数据文件，返回标准化的订单和成交数据"""
        logger.info(f"开始解析上交所数据: {self.security_id}")

        # 读取数据文件
        trade_df = pd.read_csv(trade_file) if trade_file else pd.DataFrame()
        order_df = pd.read_csv(order_file) if order_file else pd.DataFrame()

        logger.info(f"数据统计: trade={len(trade_df)}, order={len(order_df)}")

        # 解析trade数据，重构订单流（不进行合并）
        reconstructed_orders = self._reconstruct_orders_from_trades(trade_df)

        # 解析order数据（未完全成交的订单）
        pending_orders = self._parse_pending_orders(order_df)

        # 合并所有订单
        all_orders = reconstructed_orders + pending_orders

        # 按日期分组，然后在每个日期内按order_id排序
        merged_orders = self._process_orders_by_date(all_orders)

        # 解析成交数据
        trades = self._parse_trades(trade_df)

        logger.info(f"解析完成: orders={len(all_orders)} -> merged={len(merged_orders)}, trades={len(trades)}")

        return merged_orders, trades
    
    def _reconstruct_orders_from_trades(self, trade_df: pd.DataFrame) -> List[StandardizedOrder]:
        """从trade数据重构订单流"""
        orders = []

        # 按biz_index排序
        trade_df = trade_df.sort_values('biz_index')

        # 过滤集合竞价成交数据（9:30之前的trades数据不应合并到order流中）
        filtered_trades = []
        for _, row in trade_df.iterrows():
            if pd.isna(row.get('time')):
                continue

            # 解析时间，过滤9:30之前的数据
            hour, minute, second, _ = TimeParser.parse_time_string(str(row['time']))
            time_obj = time(hour, minute, second)

            # 跳过集合竞价时段的成交数据
            if time_obj < time(9, 30):
                continue

            filtered_trades.append(row)

        for row in filtered_trades:
            if pd.isna(row.get('biz_index')):
                continue

            biz_index = str(row['biz_index'])
            price = float(row['trade_price'])   # 上交所价格以分为单位，转换为元
            volume = int(row['trade_volume'])
            bs_flag = row.get('trade_bs_flag', '')
            date = str(row.get('date', ''))

            # 根据trade_bs_flag确定订单方向和原始订单号
            if bs_flag == 'B':  # 买单成交
                origin_no = str(row['trade_buy_no'])
                side = 'B'
            elif bs_flag == 'S':  # 卖单成交
                origin_no = str(row['trade_sell_no'])
                side = 'S'
            else:
                continue

            # 创建订单（这些都是立即成交的订单）
            order = StandardizedOrder(
                order_id=biz_index,  # 正确：使用biz_index作为order_id
                time=int(row['time']),  # 正确：使用time字段而不是timestamp
                price=price,
                volume=volume,
                side=side,
                order_type='A',
                origin_no=origin_no,
                date=date  # 添加date字段
            )
            orders.append(order)

        return orders
    
    def _parse_pending_orders(self, order_df: pd.DataFrame) -> List[StandardizedOrder]:
        """解析未完全成交的订单"""
        orders = []

        # 过滤有效数据
        valid_orders = order_df[pd.notna(order_df.get('biz_index', pd.Series()))]

        for _, row in valid_orders.iterrows():
            if pd.isna(row.get('order_price')) or pd.isna(row.get('order_volume')):
                continue

            order = StandardizedOrder(
                order_id=str(row.get('biz_index', '')),  # 正确：使用biz_index作为order_id
                time=int(row['time']),  # 正确：使用time字段
                price=float(row['order_price']),
                volume=int(row['order_volume']),
                side=row.get('order_side', ''),
                order_type=row.get('order_type', 'A'),
                origin_no=str(row.get('order_origin_no', '')),
                date=str(row.get('date', ''))  # 添加date字段
            )
            orders.append(order)

        return orders
    
    def _parse_trades(self, trade_df: pd.DataFrame) -> List[StandardizedTrade]:
        """解析成交数据"""
        trades = []

        for _, row in trade_df.iterrows():
            if pd.isna(row.get('biz_index')):
                continue

            trade = StandardizedTrade(
                trade_id=str(row.get('biz_index', '')),
                time=int(row['time']),  # 正确：使用time字段
                price=float(row['trade_price']) ,  # 上交所价格以分为单位，转换为元
                volume=int(row['trade_volume']),
                buy_order_id=str(row.get('trade_buy_no', '')),
                sell_order_id=str(row.get('trade_sell_no', '')),
                bs_flag=row.get('trade_bs_flag', ''),
                date=str(row.get('date', ''))  # 添加date字段
            )
            trades.append(trade)

        return trades



    def _process_orders_by_date(self, orders: List[StandardizedOrder]) -> List[StandardizedOrder]:
        """按日期分组处理订单，避免跨日期排序和合并"""
        if not orders:
            return orders

        # 按日期分组
        date_groups = {}
        for order in orders:
            date = order.date or 'unknown'
            if date not in date_groups:
                date_groups[date] = []
            date_groups[date].append(order)

        # 处理每个日期组
        final_orders = []
        for date in sorted(date_groups.keys()):  # 按日期排序处理
            date_orders = date_groups[date]

            # 在同一日期内按order_id数值排序（而不是字符串排序）
            def get_numeric_order_id(order):
                try:
                    # 尝试将order_id转换为数值进行排序
                    return float(order.order_id.strip())
                except (ValueError, AttributeError):
                    # 如果转换失败，使用字符串排序作为后备
                    return float('inf')

            date_orders.sort(key=get_numeric_order_id)

            # 在同一日期内进行订单合并
            merged_date_orders = self._merge_orders_by_origin_within_date(date_orders)

            final_orders.extend(merged_date_orders)

        return final_orders

    def _merge_orders_by_origin_within_date(self, orders: List[StandardizedOrder]) -> List[StandardizedOrder]:
        """在同一交易日内按照相同origin_no进行订单合并"""
        if not orders:
            return orders

        merged_orders = []
        origin_groups = {}

        # 按origin_no分组（仅在同一日期内）
        for order in orders:
            if order.origin_no:
                if order.origin_no not in origin_groups:
                    origin_groups[order.origin_no] = []
                origin_groups[order.origin_no].append(order)
            else:
                # 没有origin_no的订单直接添加
                merged_orders.append(order)

        # 对每个origin_no组进行合并
        for origin_no, group_orders in origin_groups.items():
            if len(group_orders) == 1:
                # 只有一个订单，直接添加
                merged_orders.append(group_orders[0])
                continue

            # 按时间排序
            group_orders.sort(key=lambda x: x.time)

            # 检查是否为连续时间戳的相同订单（大单拆分）
            merged_group = []
            current_merge = None

            for order in group_orders:
                if current_merge is None:
                    # 第一个订单
                    current_merge = order
                elif (order.side == current_merge.side and
                      order.price == current_merge.price and
                      abs(order.time - current_merge.time) <= 1000):  # 时间差在1秒内认为是连续的
                    # 合并订单
                    current_merge.volume += order.volume
                    # 保持最早的时间戳
                    current_merge.time = min(current_merge.time, order.time)
                else:
                    # 不连续，保存当前合并结果，开始新的合并
                    merged_group.append(current_merge)
                    current_merge = order

            # 添加最后一个合并结果
            if current_merge:
                merged_group.append(current_merge)

            merged_orders.extend(merged_group)

        # 在日期内重新按order_id数值排序
        def get_numeric_order_id(order):
            try:
                return float(order.order_id.strip())
            except (ValueError, AttributeError):
                return float('inf')

        merged_orders.sort(key=get_numeric_order_id)

        return merged_orders


class SZExchangeParser:
    """深交所数据解析器"""

    def __init__(self, security_id: str):
        self.security_id = security_id
        self.market = 'szl2'
        
    def parse_data_files(self, tick_file: str, trade_file: Optional[str] = None, order_file: Optional[str] = None) -> Tuple[List[StandardizedOrder], List[StandardizedTrade]]:
        """解析深交所数据文件"""
        logger.info(f"开始解析深交所数据: {self.security_id}")

        # 深交所主要从order数据生成
        order_df = pd.read_csv(order_file) if order_file else pd.DataFrame()



        logger.info(f"数据统计: order={len(order_df)}")

        # 解析订单数据
        orders = self._parse_orders(order_df)

        # 按日期分组处理订单
        processed_orders = self._process_orders_by_date(orders)

        # 深交所的trade数据通过撮合生成，这里返回空列表
        trades = []

        logger.info(f"解析完成: orders={len(processed_orders)}")

        # 检查是否需要重构集合竞价订单

        auction_orders = self._reconstruct_auction_orders(processed_orders[0].date)
        if auction_orders:
            # 将集合竞价订单插入到开头
            processed_orders = auction_orders + processed_orders
            logger.info(f"重构了 {len(auction_orders)} 个集合竞价订单")

        return processed_orders, trades
    
    def _parse_orders(self, order_df: pd.DataFrame) -> List[StandardizedOrder]:
        """解析深交所订单数据"""
        orders = []

        # 按order_index排序
        order_df = order_df.sort_values('order_index')

        for _, row in order_df.iterrows():
            # 检查必要字段
            if pd.isna(row.get('order_price')) or pd.isna(row.get('order_volume')):
                continue

            if pd.isna(row.get('order_index')) or pd.isna(row.get('order_type')):
                continue

            order_type_raw = row.get('order_type')
            price_raw = row['order_price']
            volume_raw = row['order_volume']

            # 处理价格：深交所价格以分为单位，需要转换为元
            price = float(price_raw)

            # 处理成交量
            try:
                volume = int(float(volume_raw))
                if volume <= 0:
                    continue
            except (ValueError, TypeError):
                continue

            # 深交所order_type: 1=撤单, 2=下单, U=撤单
            if str(order_type_raw) == '2' or str(order_type_raw)== '1' or str(order_type_raw) == "U":  # 下单
                if price > 0:  # 正常下单
                    order_type = 'A'
                else:  # 价格为0的异常情况
                    continue


            # 获取订单方向
            side = str(row.get('order_side', '')).strip()
            if side not in ['B', 'S']:
                continue

            # 使用order_index作为唯一标识
            order_index = int(row['order_index'])
            time = int(row.get('time', ''))  # 正确：使用time字段而不是order_index
            date = str(row.get('date', ''))

            order = StandardizedOrder(
                order_id=str(order_index),  # 正确：使用order_index作为order_id
                time=time,  # 正确：使用time字段作为时间戳
                price=price,
                volume=volume,
                side=side,
                order_type=order_type,
                origin_no=str(order_index),
                date=date  # 添加date字段
            )
            orders.append(order)

        return orders

    def _process_orders_by_date(self, orders: List[StandardizedOrder]) -> List[StandardizedOrder]:
        """按日期分组处理订单，避免跨日期排序和合并"""
        if not orders:
            return orders

        # 按日期分组
        date_groups = {}
        for order in orders:
            date = order.date or 'unknown'
            if date not in date_groups:
                date_groups[date] = []
            date_groups[date].append(order)

        # 处理每个日期组
        final_orders = []
        for date in sorted(date_groups.keys()):  # 按日期排序处理
            date_orders = date_groups[date]

            # 在同一日期内按order_id数值排序（而不是字符串排序）
            def get_numeric_order_id(order):
                try:
                    # 尝试将order_id转换为数值进行排序
                    return float(order.order_id.strip())
                except (ValueError, AttributeError):
                    # 如果转换失败，使用字符串排序作为后备
                    return float('inf')

            date_orders.sort(key=get_numeric_order_id)

            # 深交所不需要订单合并，直接添加
            final_orders.extend(date_orders)

        return final_orders


    def _parse_cancel_orders_from_trades(self, trade_df: pd.DataFrame) -> List[StandardizedOrder]:
        """从深交所trade数据中解析撤单订单"""
        orders = []
        
        if trade_df.empty:
            return orders
            
        # 按trade_index排序
        trade_df = trade_df.sort_values('trade_index')
        
        for _, row in trade_df.iterrows():
            # 检查必要字段
            if pd.isna(row.get('trade_index')):
                continue
                
            # 只处理撤单交易 (trade_bs_flag = 'C')
            bs_flag = row.get('trade_bs_flag', '')
            if str(bs_flag).strip() != 'C':
                continue
            
            # 获取撤单相关信息
            trade_index = int(row['trade_index'])
            time = int(row.get('time', 0))
            date = str(row.get('date', ''))
            
            # 对于撤单，我们需要origin_no来指向要撤销的订单
            # 深交所撤单中，被撤销的订单号在trade_buy_no或trade_sell_no中，另一个会是"0.000000"
            buy_no = str(row.get('trade_buy_no', ''))
            sell_no = str(row.get('trade_sell_no', ''))
            
            # origin_no应该是非0的那个订单号
            origin_no = buy_no if buy_no and buy_no != "0.000000" else sell_no if sell_no and sell_no != "0.000000" else ""
            
            if not origin_no:
                continue
                
            # 创建撤单订单
            order = StandardizedOrder(
                order_id=str(trade_index),  # 撤单本身的ID
                time=time,
                price=0.0,  # 撤单价格为0
                volume=0,   # 撤单数量为0
                side='B',   # 撤单方向可以是任意的，因为会被忽略
                order_type='D',  # 撤单类型
                origin_no=origin_no,  # 要撤销的订单ID
                date=date
            )
            orders.append(order)
        
        return orders

    def _reconstruct_auction_orders(self, date: str) -> List[StandardizedOrder]:
        """重构集合竞价订单"""
        # 这里需要根据官方tick数据的第一个时间点来重构
        # 暂时返回空列表，后续可以根据需要实现
        logger.info("集合竞价订单重构功能待实现")
        return []


