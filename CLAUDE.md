# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a financial market backtesting system that processes Level 2 market data from Shanghai and Shenzhen stock exchanges. The system implements order book management, price-time priority matching engine, and generates market snapshots for validation against official exchange data.

## Architecture Overview

The system is organized into several key modules:

1. **Data Parser** (`data_parser.py`): Handles parsing and standardization of exchange-specific L2 data formats
2. **Matching Engine** (`matching_engine.py`): Implements order book and price-time priority matching logic
3. **Snapshot Generator** (`snapshot_generator.py`): Creates market depth snapshots at configurable intervals
4. **Validation Module** (`validation_module.py`): Compares generated data against official exchange data
5. **Advanced Matching System** (`advanced_matching_system.py`): Main controller that orchestrates the entire process

## Key Components

### Data Flow
1. Raw exchange data files (CSV) → Data Parser → Standardized Orders/Trades
2. Standardized data → Matching Engine → Order Book Updates & Trades
3. Order book state → Snapshot Generator → Market Snapshots
4. Generated data → Validation Module → Comparison Reports

### Exchange Support
- **Shanghai Exchange (shl2)**: Processes tick, trade, and order data files
- **Shenzhen Exchange (szl2)**: Processes tick and order data files (no separate trade file)

## Development Commands

### Running Tests
```bash
python test_comprehensive_matching.py
```

### Processing Data
```bash
python advanced_matching_system.py
```

### Checking Data
```bash
python check_snapshot_data.py
```

### Testing Auction Fix
```bash
python test_auction_fix.py
```

## Key Features

1. **Multi-market Support**: Handles both Shanghai and Shenzhen exchange data formats
2. **Configurable Snapshot Generation**: Event-driven, fixed-interval, or specified-times modes
3. **Order Book Management**: High-performance order book using SortedDict for price sorting
4. **Trading Phase Awareness**: Distinguishes between continuous trading and auction periods
5. **Data Validation**: Comprehensive validation against official exchange data
6. **Date Isolation**: Properly handles multi-day data with date-based order book resets

## Common Development Tasks

1. **Adding New Market Support**: Extend data parser with new exchange-specific logic
2. **Modifying Matching Logic**: Update MatchingEngine class for custom matching rules
3. **Enhancing Validation**: Extend validation_module with additional field comparisons
4. **Performance Optimization**: Optimize order book operations in matching_engine.py

## Data Structure Notes

- All prices are stored in yuan (converted from exchange cents)
- Time values are stored as integers (HHMMSSmmm format)
- Order IDs are preserved for traceability
- Market depth snapshots maintain 10 levels by default