#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试集中竞价修复效果
"""

import pandas as pd
import numpy as np
from advanced_matching_system import AdvancedMatchingSystem, ProcessingConfig
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_auction_fix():
    """测试集中竞价修复效果"""
    print("\n=== 测试集中竞价修复效果 ===")
    
    # 创建配置
    config = ProcessingConfig.create_event_driven_config('output')
    config.progress_report_interval = 1000000  # 减少进度报告频率
    
    # 创建系统
    system = AdvancedMatchingSystem(config)
    
    # 深交所数据配置（较小的数据集，便于测试）
    sz_config = {
        'security_id': '161116',
        'market': 'szl2',
        'data_files': {
            'tick': 'data/hq-szl2-161116-1-20250807165417054.csv',
            'trade': 'data/hq-szl2-161116-3-20250807134652012.csv',
            'order': 'data/hq-szl2-161116-4-20250807134642187.csv'
        }
    }
    
    start_time = time.time()
    
    try:
        # 处理深交所数据
        result = system.process_security(
            security_id=sz_config['security_id'],
            market=sz_config['market'],
            data_files=sz_config['data_files']
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  处理时间: {processing_time:.2f}秒")
        
        if 'processing_stats' in result:
            stats = result['processing_stats']
            print(f"  订单处理: {stats.get('orders_processed', 0)}")
            print(f"  成交生成: {stats.get('trades_generated', 0)}")
            print(f"  快照生成: {stats.get('snapshots_generated', 0)}")
        
        # 分析生成的成交数据
        analyze_generated_trades()
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_generated_trades():
    """分析生成的成交数据"""
    print("\n=== 分析生成的成交数据 ===")
    
    try:
        # 读取生成的成交数据
        trades_file = 'output/trades_161116_szl2.csv'
        trades_df = pd.read_csv(trades_file)
        
        print(f"总成交记录: {len(trades_df)} 条")
        
        if len(trades_df) == 0:
            print("没有生成成交数据")
            return
        
        # 分析集中竞价成交
        auction_trades = trades_df[trades_df['trade_id'].str.contains('auction', na=False)]
        print(f"集中竞价成交: {len(auction_trades)} 条")
        
        if len(auction_trades) > 0:
            print("\n集中竞价成交详情:")
            for _, trade in auction_trades.iterrows():
                print(f"  时间: {trade['time']}, 价格: {trade['price']:.2f}, "
                      f"数量: {trade['volume']}, 买单ID: {trade['buy_order_id']}, "
                      f"卖单ID: {trade['sell_order_id']}, BS标志: {trade['bs_flag']}")
        
        # 分析时间分布
        print(f"\n成交时间分布:")
        time_counts = trades_df['time'].value_counts().head(10)
        for time_val, count in time_counts.items():
            print(f"  {time_val}: {count} 条")
        
        # 检查是否有9:25:00和15:00:00的成交
        opening_auction = trades_df[trades_df['time'] == 92500000]
        closing_auction = trades_df[trades_df['time'] == 150000000]
        
        print(f"\n开盘集中竞价成交 (9:25:00): {len(opening_auction)} 条")
        print(f"收盘集中竞价成交 (15:00:00): {len(closing_auction)} 条")
        
        # 检查买卖单ID是否正确配对
        auction_with_real_ids = auction_trades[
            (auction_trades['buy_order_id'] != 'AUCTION') & 
            (auction_trades['sell_order_id'] != 'AUCTION')
        ]
        print(f"正确配对的集中竞价成交: {len(auction_with_real_ids)} 条")
        
    except Exception as e:
        print(f"分析失败: {e}")


def compare_with_official_data():
    """与官方数据对比"""
    print("\n=== 与官方数据对比 ===")
    
    try:
        # 读取官方成交数据
        official_trades = pd.read_csv('data/hq-szl2-161116-3-20250807134652012.csv')
        generated_trades = pd.read_csv('output/trades_161116_szl2.csv')
        
        print(f"官方成交数据: {len(official_trades)} 条")
        print(f"生成成交数据: {len(generated_trades)} 条")
        
        # 分析开盘和收盘时段的成交
        official_opening = official_trades[
            (official_trades['time'] >= 92500000) & 
            (official_trades['time'] <= 93000000)
        ]
        official_closing = official_trades[
            (official_trades['time'] >= 145700000) & 
            (official_trades['time'] <= 150000000)
        ]
        
        generated_opening = generated_trades[
            (generated_trades['time'] >= 92500000) & 
            (generated_trades['time'] <= 93000000)
        ]
        generated_closing = generated_trades[
            (generated_trades['time'] >= 145700000) & 
            (generated_trades['time'] <= 150000000)
        ]
        
        print(f"\n开盘时段成交对比:")
        print(f"  官方: {len(official_opening)} 条")
        print(f"  生成: {len(generated_opening)} 条")
        
        print(f"\n收盘时段成交对比:")
        print(f"  官方: {len(official_closing)} 条")
        print(f"  生成: {len(generated_closing)} 条")
        
    except Exception as e:
        print(f"对比失败: {e}")


def main():
    """主函数"""
    print("=== 集中竞价修复测试 ===")
    
    # 测试集中竞价修复
    success = test_auction_fix()
    
    if success:
        # 与官方数据对比
        compare_with_official_data()
    
    # 总结
    print(f"\n=== 测试总结 ===")
    print(f"集中竞价修复测试: {'✅ 成功' if success else '❌ 失败'}")


if __name__ == "__main__":
    main()
