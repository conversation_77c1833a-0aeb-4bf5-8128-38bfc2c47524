#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
撮合引擎模块
实现订单簿管理和价格时间优先撮合逻辑
"""

import numpy as np
from collections import deque
from sortedcontainers import SortedDict
from typing import Dict, List, Tuple, Optional
import logging
from data_parser import StandardizedOrder, StandardizedTrade

logger = logging.getLogger(__name__)


class OrderBook:
    """高性能订单簿"""
    
    def __init__(self):
        # 使用SortedDict实现高效的价格排序
        self.buy_orders = SortedDict()  # 买单：价格从高到低
        self.sell_orders = SortedDict()  # 卖单：价格从低到高
        self.order_sequence = 0
        self.orders_by_id = {}  # 订单ID索引
        
    def add_order(self, order: StandardizedOrder) -> bool:
        """添加订单到订单簿"""
        try:
            self.order_sequence += 1
            
            order_info = {
                'order_id': order.order_id,  # 使用order_id作为订单在订单簿中的标识
                'price': order.price,
                'volume': order.volume,
                'side': order.side,
                'sequence': self.order_sequence,
                'timestamp': order.time,
                'original_volume': order.volume
            }
            
            # 添加到价格层
            if order.side == 'B':  # 买单
                if order.price not in self.buy_orders:
                    self.buy_orders[order.price] = deque()
                self.buy_orders[order.price].append(order_info)
            else:  # 卖单
                if order.price not in self.sell_orders:
                    self.sell_orders[order.price] = deque()
                self.sell_orders[order.price].append(order_info)
            
            # 添加到ID索引，同时用order_id和origin_no索引
            self.orders_by_id[order.order_id] = order_info
            if order.origin_no:
                self.orders_by_id[order.origin_no] = order_info
            
            return True
            
        except Exception as e:
            logger.error(f"添加订单失败: {e}")
            return False
    
    def cancel_order(self, origin_no: str, price: float = None, side: str = None) -> bool:
        """撤销订单 - 使用origin_no查找要撤销的订单"""
        try:
            # 使用origin_no从ID索引查找要撤销的订单
            if origin_no in self.orders_by_id:
                order_info = self.orders_by_id[origin_no]
                price = order_info['price']
                side = order_info['side']
                actual_order_id = order_info['order_id']  # 获取实际在订单簿中的订单ID
                
                logger.info(f"找到要撤销的订单: origin_no={origin_no}, actual_order_id={actual_order_id}, price={price}, side={side}")
                
                # 从价格层移除
                orders_dict = self.buy_orders if side == 'B' else self.sell_orders
                
                if price in orders_dict:
                    orders_queue = orders_dict[price]
                    found = False
                    for i, order in enumerate(orders_queue):
                        if order['order_id'] == actual_order_id:
                            del orders_queue[i]
                            if not orders_queue:
                                del orders_dict[price]
                            found = True
                            logger.info(f"从价格层移除订单成功: origin_no={origin_no}, price={price}")
                            break
                    
                    if not found:
                        logger.warning(f"在价格层中未找到订单: origin_no={origin_no}, actual_order_id={actual_order_id}, price={price}")
                else:
                    logger.warning(f"价格层中不存在该价格: origin_no={origin_no}, price={price}")
                
                # 从ID索引移除
                # 移除origin_no索引
                if origin_no in self.orders_by_id:
                    del self.orders_by_id[origin_no]
                    logger.info(f"从ID索引移除origin_no: {origin_no}")
                # 如果actual_order_id不同，也要移除
                if actual_order_id in self.orders_by_id and actual_order_id != origin_no:
                    del self.orders_by_id[actual_order_id]
                    logger.info(f"从ID索引移除actual_order_id: {actual_order_id}")
                
                return True
            else:
                logger.warning(f"未找到要撤销的订单: origin_no={origin_no}")
            
            return False
            
        except Exception as e:
            logger.error(f"撤销订单失败: {e}")
            return False
    
    def get_best_bid(self) -> Optional[float]:
        """获取最优买价"""
        return self.buy_orders.peekitem(-1)[0] if self.buy_orders else None
    
    def get_best_ask(self) -> Optional[float]:
        """获取最优卖价"""
        return self.sell_orders.peekitem(0)[0] if self.sell_orders else None
    
    def get_market_depth(self, levels: int = 10) -> Tuple[List[float], List[int], List[float], List[int]]:
        """获取市场深度数据"""
        bid_prices, bid_volumes = [], []
        ask_prices, ask_volumes = [], []
        
        # 买单深度（价格从高到低）
        for i, (price, orders) in enumerate(reversed(self.buy_orders.items())):
            if i >= levels:
                break
            total_volume = sum(order['volume'] for order in orders)
            if total_volume > 0:
                bid_prices.append(price)
                bid_volumes.append(total_volume)
        
        # 卖单深度（价格从低到高）
        for i, (price, orders) in enumerate(self.sell_orders.items()):
            if i >= levels:
                break
            total_volume = sum(order['volume'] for order in orders)
            if total_volume > 0:
                ask_prices.append(price)
                ask_volumes.append(total_volume)
        
        # 补齐到指定层数
        while len(bid_prices) < levels:
            bid_prices.append(0.0)
            bid_volumes.append(0)
        while len(ask_prices) < levels:
            ask_prices.append(0.0)
            ask_volumes.append(0)
            
        return bid_prices, bid_volumes, ask_prices, ask_volumes
    
    def get_total_volume(self) -> Tuple[int, int]:
        """获取总买卖量"""
        total_bid_qty = sum(
            sum(order['volume'] for order in orders)
            for orders in self.buy_orders.values()
        )
        total_offer_qty = sum(
            sum(order['volume'] for order in orders)
            for orders in self.sell_orders.values()
        )
        return total_bid_qty, total_offer_qty


class MatchingEngine:
    """统一撮合引擎"""

    def __init__(self):
        self.order_book = OrderBook()
        self.trades = []
        self.current_price = 0.0
        self.total_volume = 0
        self.total_value = 0.0
        self.num_trades = 0
        self.current_date = None  # 当前交易日期，用于日期隔离
        
    def process_order(self, order: StandardizedOrder, trading_phase: str) -> List[StandardizedTrade]:
        """处理订单"""
        # 检查日期隔离：如果是新的交易日，重置订单簿
        if order.date and order.date != self.current_date:
            if self.current_date is not None:  # 只在非首次时打印日志
                logger.info(f"检测到新交易日: {order.date}，重置订单簿")
            self._reset_for_new_date(order.date)

        if trading_phase in ['C', 'C111']:  # 集合竞价阶段
            return self._process_call_auction_order(order)
        else:  # 连续竞价阶段
            return self._process_continuous_order(order)
    
    def _process_call_auction_order(self, order: StandardizedOrder) -> List[StandardizedTrade]:
        """处理集合竞价订单（只接受不撮合）"""
        if order.order_type == 'A':  # 新增订单
            self.order_book.add_order(order)
        elif order.order_type == 'D':  # 撤单
            # 对于撤单，直接使用origin_no来查找要撤销的订单
            logger.info(f"处理集合竞价撤单订单: order_id={order.order_id}, origin_no={order.origin_no}, time={order.time}")
            if order.origin_no:
                self.order_book.cancel_order(order.origin_no)
        
        return []  # 集合竞价阶段不产生成交
    
    def _process_continuous_order(self, order: StandardizedOrder) -> List[StandardizedTrade]:
        """处理连续竞价订单（实时撮合）"""
        trades = []
        
        if order.order_type == 'A':  # 新增订单
            trades = self._match_order(order)
        elif order.order_type == 'D':  # 撤单
            # 对于撤单，直接使用origin_no来查找要撤销的订单
            logger.info(f"处理撤单订单: order_id={order.order_id}, origin_no={order.origin_no}, time={order.time}")
            if order.origin_no:
                self.order_book.cancel_order(order.origin_no)
        
        return trades
    
    def _match_order(self, order: StandardizedOrder) -> List[StandardizedTrade]:
        """撮合订单"""
        trades = []
        remaining_volume = order.volume
        
        if order.side == 'B':  # 买单
            # 与卖单撮合
            while remaining_volume > 0 and self.order_book.sell_orders:
                best_ask = self.order_book.get_best_ask()
                if best_ask is None or order.price < best_ask:
                    break
                
                # 执行撮合
                sell_orders = self.order_book.sell_orders[best_ask]
                while remaining_volume > 0 and sell_orders:
                    sell_order = sell_orders[0]
                    trade_volume = min(remaining_volume, sell_order['volume'])
                    trade_price = best_ask
                    
                    # 创建成交记录
                    trade = StandardizedTrade(
                        trade_id=f"trade_{self.num_trades + 1}",
                        time=order.time,
                        price=trade_price,
                        volume=trade_volume,
                        buy_order_id=order.order_id,
                        sell_order_id=sell_order['order_id'],
                        bs_flag='B',
                        date=order.date
                    )
                    trades.append(trade)
                    self.trades.append(trade)  # 添加到引擎的成交记录中

                    # 更新统计信息
                    self.current_price = trade_price
                    self.total_volume += trade_volume
                    self.total_value += trade_price * trade_volume
                    self.num_trades += 1
                    
                    # 更新订单
                    remaining_volume -= trade_volume
                    sell_order['volume'] -= trade_volume
                    
                    if sell_order['volume'] == 0:
                        sell_orders.popleft()
                        # 从ID索引移除
                        if sell_order['order_id'] in self.order_book.orders_by_id:
                            del self.order_book.orders_by_id[sell_order['order_id']]
                
                if not sell_orders:
                    del self.order_book.sell_orders[best_ask]
        
        else:  # 卖单
            # 与买单撮合
            while remaining_volume > 0 and self.order_book.buy_orders:
                best_bid = self.order_book.get_best_bid()
                if best_bid is None or order.price > best_bid:
                    break
                
                # 执行撮合
                buy_orders = self.order_book.buy_orders[best_bid]
                while remaining_volume > 0 and buy_orders:
                    buy_order = buy_orders[0]
                    trade_volume = min(remaining_volume, buy_order['volume'])
                    trade_price = best_bid
                    
                    # 创建成交记录
                    trade = StandardizedTrade(
                        trade_id=f"trade_{self.num_trades + 1}",
                        time=order.time,
                        price=trade_price,
                        volume=trade_volume,
                        buy_order_id=buy_order['order_id'],
                        sell_order_id=order.order_id,
                        bs_flag='S',
                        date=order.date
                    )
                    trades.append(trade)
                    self.trades.append(trade)  # 添加到引擎的成交记录中

                    # 更新统计信息
                    self.current_price = trade_price
                    self.total_volume += trade_volume
                    self.total_value += trade_price * trade_volume
                    self.num_trades += 1
                    
                    # 更新订单
                    remaining_volume -= trade_volume
                    buy_order['volume'] -= trade_volume
                    
                    if buy_order['volume'] == 0:
                        buy_orders.popleft()
                        # 从ID索引移除
                        if buy_order['order_id'] in self.order_book.orders_by_id:
                            del self.order_book.orders_by_id[buy_order['order_id']]
                
                if not buy_orders:
                    del self.order_book.buy_orders[best_bid]
        
        # 如果还有剩余量，加入订单簿
        if remaining_volume > 0:
            remaining_order = StandardizedOrder(
                order_id=order.order_id,
                time=order.time,
                price=order.price,
                volume=remaining_volume,
                side=order.side,
                order_type='A',
                date=order.date
            )
            self.order_book.add_order(remaining_order)
        
        return trades
    
    def call_auction_match(self, auction_time: int = 92500000) -> List[StandardizedTrade]:
        """集合竞价撮合 - 正确配对买单和卖单"""
        trades = []

        # 计算集合竞价价格（最大成交量原则）
        auction_price = self._calculate_auction_price()
        if auction_price is None:
            return trades

        # 收集所有可成交的买单和卖单
        eligible_buy_orders = []
        eligible_sell_orders = []

        # 收集可成交的买单（价格 >= 集合竞价价格）
        for price in self.order_book.buy_orders:
            if price >= auction_price:
                for order in self.order_book.buy_orders[price]:
                    eligible_buy_orders.append({
                        'order_id': order['order_id'],
                        'price': price,
                        'volume': order['volume'],
                        'timestamp': order['timestamp']
                    })

        # 收集可成交的卖单（价格 <= 集合竞价价格）
        for price in self.order_book.sell_orders:
            if price <= auction_price:
                for order in self.order_book.sell_orders[price]:
                    eligible_sell_orders.append({
                        'order_id': order['order_id'],
                        'price': price,
                        'volume': order['volume'],
                        'timestamp': order['timestamp']
                    })

        # 按价格优先、时间优先排序
        eligible_buy_orders.sort(key=lambda x: (-x['price'], x['timestamp']))  # 买单：价格高优先，时间早优先
        eligible_sell_orders.sort(key=lambda x: (x['price'], x['timestamp']))  # 卖单：价格低优先，时间早优先

        # 执行撮合
        buy_idx = 0
        sell_idx = 0

        while buy_idx < len(eligible_buy_orders) and sell_idx < len(eligible_sell_orders):
            buy_order = eligible_buy_orders[buy_idx]
            sell_order = eligible_sell_orders[sell_idx]

            # 计算成交量
            trade_volume = min(buy_order['volume'], sell_order['volume'])

            # 创建成交记录
            trade = StandardizedTrade(
                trade_id=f"auction_trade_{len(trades) + 1}",
                time=auction_time,  # 使用集合竞价时间
                price=auction_price,
                volume=trade_volume,
                buy_order_id=buy_order['order_id'],
                sell_order_id=sell_order['order_id'],
                bs_flag='B' ,#if float(buy_order['order_id']) > float(sell_order['order_id']) else 'S' ,  # 集合竞价成交标记为买方主动
                date=self.current_date
            )
            trades.append(trade)
            self.trades.append(trade)

            # 更新订单量
            buy_order['volume'] -= trade_volume
            sell_order['volume'] -= trade_volume

            # 移除已完全成交的订单
            if buy_order['volume'] == 0:
                buy_idx += 1
            if sell_order['volume'] == 0:
                sell_idx += 1

        # 从订单簿中移除已成交的订单
        self._remove_matched_orders_from_book(eligible_buy_orders, eligible_sell_orders)

        # 更新统计信息
        if trades:
            total_volume = sum(trade.volume for trade in trades)
            self.current_price = auction_price
            self.total_volume += total_volume
            self.total_value += auction_price * total_volume
            self.num_trades += len(trades)

        return trades

    def _remove_matched_orders_from_book(self, buy_orders: List[Dict], sell_orders: List[Dict]):
        """从订单簿中移除已成交的订单"""
        # 移除已完全成交的买单
        for buy_order in buy_orders:
            if buy_order['volume'] == 0:
                order_id = buy_order['order_id']
                price = buy_order['price']

                if price in self.order_book.buy_orders:
                    orders = self.order_book.buy_orders[price]
                    # 找到并移除对应的订单
                    for i, order in enumerate(orders):
                        if order['order_id'] == order_id:
                            orders_list = list(orders)
                            orders_list.pop(i)
                            orders.clear()
                            orders.extend(orders_list)
                            break

                    # 如果该价格档位没有订单了，删除该价格档位
                    if not orders:
                        del self.order_book.buy_orders[price]

                # 从ID索引中移除
                if order_id in self.order_book.orders_by_id:
                    del self.order_book.orders_by_id[order_id]

        # 移除已完全成交的卖单
        for sell_order in sell_orders:
            if sell_order['volume'] == 0:
                order_id = sell_order['order_id']
                price = sell_order['price']

                if price in self.order_book.sell_orders:
                    orders = self.order_book.sell_orders[price]
                    # 找到并移除对应的订单
                    for i, order in enumerate(orders):
                        if order['order_id'] == order_id:
                            orders_list = list(orders)
                            orders_list.pop(i)
                            orders.clear()
                            orders.extend(orders_list)
                            break

                    # 如果该价格档位没有订单了，删除该价格档位
                    if not orders:
                        del self.order_book.sell_orders[price]

                # 从ID索引中移除
                if order_id in self.order_book.orders_by_id:
                    del self.order_book.orders_by_id[order_id]

    def _reset_for_new_date(self, new_date: str):
        """为新交易日重置撮合引擎状态"""
        # 重置订单簿
        self.order_book = OrderBook()

        # 更新当前日期
        self.current_date = new_date

        # 注意：不重置trades列表，因为我们需要保留所有历史成交记录
        # 也不重置统计信息，因为这些可能需要跨日累计

        logger.info(f"撮合引擎已重置为新交易日: {new_date}")
    
    def _calculate_auction_price(self) -> Optional[float]:
        """计算集合竞价价格（最大成交量原则）"""
        if not self.order_book.buy_orders or not self.order_book.sell_orders:
            return None
        
        # 获取所有可能的价格点
        all_prices = set()
        all_prices.update(self.order_book.buy_orders.keys())
        all_prices.update(self.order_book.sell_orders.keys())
        all_prices = sorted(all_prices)
        
        max_volume = 0
        best_price = None
        
        for price in all_prices:
            # 计算在此价格下的可成交量
            buy_volume = sum(
                sum(order['volume'] for order in orders)
                for buy_price, orders in self.order_book.buy_orders.items()
                if buy_price >= price
            )
            
            sell_volume = sum(
                sum(order['volume'] for order in orders)
                for sell_price, orders in self.order_book.sell_orders.items()
                if sell_price <= price
            )
            
            match_volume = min(buy_volume, sell_volume)
            
            if match_volume > max_volume:
                max_volume = match_volume
                best_price = price
        
        return best_price
